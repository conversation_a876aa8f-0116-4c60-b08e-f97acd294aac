{"name": "admin", "version": "0.0.0", "dependencies": {"angular": "^1.4.0", "angular-animate": "^1.4.0", "angular-cookies": "^1.4.0", "angular-resource": "^1.4.0", "angular-route": "^1.4.0", "angular-sanitize": "^1.4.0", "angular-touch": "^1.4.0", "angular-material": "^1.1.1", "angular-ui-router": "^0.3.2", "angularfire": "^2.2.0", "angular-material-data-table": "^0.10.10", "underscore": "^1.8.3", "html2canvas": "^0.4.1", "pdfmake": "^0.1.24", "ng-file-upload": "^12.2.13", "bootstrap-sass": "3.4.1"}, "devDependencies": {"angular-mocks": "^1.4.0"}, "appPath": "app", "moduleName": "adminApp", "overrides": {"bootstrap": {"main": ["less/bootstrap.less", "dist/css/bootstrap.css", "dist/js/bootstrap.js"]}}}