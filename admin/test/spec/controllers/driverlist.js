'use strict';

describe('Controller: DriverlistCtrl', function () {

  // load the controller's module
  beforeEach(module('adminApp'));

  var DriverlistCtrl,
    scope;

  // Initialize the controller and a mock scope
  beforeEach(inject(function ($controller, $rootScope) {
    scope = $rootScope.$new();
    DriverlistCtrl = $controller('DriverlistCtrl', {
      $scope: scope
      // place here mocked dependencies
    });
  }));

  it('should attach a list of awesomeThings to the scope', function () {
    expect(DriverlistCtrl.awesomeThings.length).toBe(3);
  });
});
