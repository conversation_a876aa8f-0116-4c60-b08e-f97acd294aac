'use strict';

describe('Controller: AdminCtrl', function () {

  // load the controller's module
  beforeEach(module('adminApp'));

  var AdminCtrl,
    scope;

  // Initialize the controller and a mock scope
  beforeEach(inject(function ($controller, $rootScope) {
    scope = $rootScope.$new();
    AdminCtrl = $controller('AdminCtrl', {
      $scope: scope
      // place here mocked dependencies
    });
  }));

  it('should attach a list of awesomeThings to the scope', function () {
    expect(AdminCtrl.awesomeThings.length).toBe(3);
  });
});
