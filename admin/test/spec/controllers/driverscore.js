'use strict';

describe('Controller: DriverscoreCtrl', function () {

  // load the controller's module
  beforeEach(module('adminApp'));

  var DriverscoreCtrl,
    scope;

  // Initialize the controller and a mock scope
  beforeEach(inject(function ($controller, $rootScope) {
    scope = $rootScope.$new();
    DriverscoreCtrl = $controller('DriverscoreCtrl', {
      $scope: scope
      // place here mocked dependencies
    });
  }));

  it('should attach a list of awesomeThings to the scope', function () {
    expect(DriverscoreCtrl.awesomeThings.length).toBe(3);
  });
});
