'use strict';

describe('Controller: EventselectorCtrl', function () {

  // load the controller's module
  beforeEach(module('adminApp'));

  var EventselectorCtrl,
    scope;

  // Initialize the controller and a mock scope
  beforeEach(inject(function ($controller, $rootScope) {
    scope = $rootScope.$new();
    EventselectorCtrl = $controller('EventselectorCtrl', {
      $scope: scope
      // place here mocked dependencies
    });
  }));

  it('should attach a list of awesomeThings to the scope', function () {
    expect(EventselectorCtrl.awesomeThings.length).toBe(3);
  });
});
