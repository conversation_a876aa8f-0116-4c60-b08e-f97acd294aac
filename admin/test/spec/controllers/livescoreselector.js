'use strict';

describe('Controller: LivescoreselectorCtrl', function () {

  // load the controller's module
  beforeEach(module('adminApp'));

  var LivescoreselectorCtrl,
    scope;

  // Initialize the controller and a mock scope
  beforeEach(inject(function ($controller, $rootScope) {
    scope = $rootScope.$new();
    LivescoreselectorCtrl = $controller('LivescoreselectorCtrl', {
      $scope: scope
      // place here mocked dependencies
    });
  }));

  it('should attach a list of awesomeThings to the scope', function () {
    expect(LivescoreselectorCtrl.awesomeThings.length).toBe(3);
  });
});
