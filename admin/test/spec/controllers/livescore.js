'use strict';

describe('Controller: LivescoreCtrl', function () {

  // load the controller's module
  beforeEach(module('adminApp'));

  var LivescoreCtrl,
    scope;

  // Initialize the controller and a mock scope
  beforeEach(inject(function ($controller, $rootScope) {
    scope = $rootScope.$new();
    LivescoreCtrl = $controller('LivescoreCtrl', {
      $scope: scope
      // place here mocked dependencies
    });
  }));

  it('should attach a list of awesomeThings to the scope', function () {
    expect(LivescoreCtrl.awesomeThings.length).toBe(3);
  });
});
