'use strict';

/**
 * @ngdoc directive
 * @name adminApp.directive:livescoreview
 * @description
 * # livescoreview
 */
angular.module('adminApp')
  .directive('livescoreview', function () {
    return {
      templateUrl: 'views/partials/livescores.html',
      restrict: 'E',
      controller: 'LivescoreCtrl',
      controllerAs: 'vm',
      scope: {
        round : '=',
        isprint: '='
      },
      link: function postLink(scope, element, attrs, controller) {

      }
    };
  });
