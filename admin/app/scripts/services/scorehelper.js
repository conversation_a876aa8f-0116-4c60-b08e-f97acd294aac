'use strict';

/**
 * @ngdoc service
 * @name adminApp.scorehelper
 * @description
 * # scorehelper
 * Service in the adminApp.
 */
angular.module('adminApp')
  .service('scorehelper', function ($q) {

    function sortScores(driverScores, firstRoundScores) {

      driverScores = driverScores.sort(function (a, b) {

        // sections higher is better
        if (a.sectionCount > b.sectionCount) {
          return -1;
        } else if (a.sectionCount < b.sectionCount) {
          return 1;
        }

        if (a.pointCount > b.pointCount) {
          return 1;
        } else if (a.pointCount < b.pointCount) {
          return -1;
        }

        if (a.zeroCount > b.zeroCount) {
          return -1;
        } else if (a.zeroCount < b.zeroCount) {
          return 1;
        }

        if (a.oneCount > b.oneCount) {
          return -1;
        } else if (a.oneCount < b.oneCount) {
          return 1;
        }

        if (a.twoCount > b.twoCount) {
          return -1;
        } else if (a.twoCount < b.twoCount) {
          return 1;
        }

        if (a.threeCount > b.threeCount) {
          return -1;
        } else if (a.threeCount < b.threeCount) {
          return 1;
        }

        if (a.fiveCount > b.fiveCount) {
          return -1;
        } else if (a.fiveCount < b.fiveCount) {
          return 1;
        }

        if (a.tenCount > b.tenCount) {
          return -1;
        } else if (a.tenCount < b.tenCount) {
          return 1;
        }


        if (a.timeCount && b.timeCount) {
          if (a.timeCount > b.timeCount) {
            return 1;
          } else if (a.timeCount < b.timeCount) {
            return -1;
          }
        }


        if (firstRoundScores) {
          // compare the two drivers in the first round.
          var firstRoundDriverA;
          var firstRoundDriverB;

          _.each(firstRoundScores, function (el, i) {
            if (el.driver.driverId === a.driver.driverId) {
              firstRoundDriverA = i;
            }
          });

          _.each(firstRoundScores, function (el, i) {
            if (el.driver.driverId === b.driver.driverId) {
              firstRoundDriverB = i;
            }
          });

          if (firstRoundDriverA < firstRoundDriverB) {
            return -1;
          } else if (firstRoundDriverA > firstRoundDriverB) {
            return 1;
          }

        }

        try {
          if (parseInt(a.driver.driverNumber) > parseInt(b.driver.driverNumber)) {
            return 1;
          } else {
            return -1;
          }
        } catch (ex) {

        }

        if (a.driver.suddenDeathQualified === true) {
          return -1;
        } else if (b.driver.suddenDeathQualified === true) {
          return 1;
        }

        return 0;

      });

      return driverScores;

    }

    function getSortedScoreForRound(event, round) {

      var deferred = $q.defer();

      var driverScores = [];

      _.each(event.drivers, function (driver) {

        var driverScore = {
          round: round,
          driver: driver,
          sectionCount: 0,
          pointCount: 0,
          zeroCount: 0,
          oneCount: 0,
          twoCount: 0,
          threeCount: 0,
          fiveCount: 0,
          tenCount: 0,
          timeCount: 0
        };

        if (driver.scores) {
          _.each(driver.scores[round], function (score, section) {

            if (section > 0 && score != undefined) {
              driverScore.sectionCount++;

              if (section != 10 && score) {
                driverScore.pointCount += score;
              }
              if (score == 0) {
                driverScore.zeroCount++;
              }
              if (score == 1) {
                driverScore.oneCount++;
              }
              if (score == 2) {
                driverScore.twoCount++;
              }
              if (score == 3) {
                driverScore.threeCount++;
              }
              if (score == 5) {
                driverScore.fiveCount++;
              }
              if (score == 10) {
                driverScore.tenCount++;
              }

              if (section == 10) {
                driverScore.timeCount = score;
              }
            }
          });
        }

        driverScores.push(driverScore);

      });

      driverScores = sortScores(driverScores);


      if (round == 2) {
        getSortedScoreForRound(event, 1).then(function (firstRoundScores) {

          var firstRoundQualified = firstRoundScores.slice(0, event.preQualified);
          // add sudden death;
          firstRoundScores.map(function (score) {
            if (score.driver.suddenDeathQualified === true) {
              firstRoundQualified.push(score);
            }
          });

          var res = [];
          _.each(firstRoundQualified, function (driverScoreFirstRound) {
            res.push(driverScores.find(function (ds) {
              return ds.driver.driverId === driverScoreFirstRound.driver.driverId;
            }));
          });

          deferred.resolve(sortScores(res, firstRoundScores));


        });
      } else {
        deferred.resolve(driverScores);
      }

      return deferred.promise;


    }

    return {
      getSortedScoreForRound: getSortedScoreForRound,
      sortScores: sortScores
    };
  });
