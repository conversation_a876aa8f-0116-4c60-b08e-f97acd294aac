'use strict';

/**
 * @ngdoc function
 * @name adminApp.controller:EventselectorCtrl
 * @description
 * # EventselectorCtrl
 * Controller of the adminApp
 */
angular.module('adminApp')
  .controller('EventselectorCtrl', function ($state, $q, $firebaseObject, $firebaseArray) {

    var vm = this;

    vm.db = firebase.database().ref();

    vm.allEvents = [];

    vm.eventChanged = function() {

      $state.go('print.event', {
        eventId: vm.selectedEvent
      });

    };

    vm.loadEvents = function () {
      var deferred = $q.defer();

      var events = vm.db.child('events');

      vm.allEvents = $firebaseArray(events);
      vm.allEvents.$loaded().then(function () {
        deferred.resolve(vm.allEvents);
      });

      return deferred.promise;
    };

    vm.loadEvents();



  });
