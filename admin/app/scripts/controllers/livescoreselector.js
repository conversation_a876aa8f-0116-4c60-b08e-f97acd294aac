'use strict';

/**
 * @ngdoc function
 * @name adminApp.controller:LivescoreselectorCtrl
 * @description
 * # LivescoreselectorCtrl
 * Controller of the adminApp
 */
angular.module('adminApp')

  .config(function ($stateProvider) {

    $stateProvider
      .state('livescores', {
        url: "/livescores",
        templateUrl: "views/livescoreSelector.html"
      });
  })

  .controller('LivescoreselectorCtrl', function ($firebaseArray, $q, $state) {
    var vm = this;
    vm.db = firebase.database().ref();

    vm.eventChanged = function (event) {
      if (event) {
        $state.go('livescore', {eventId: event.$id});
      } else {
        $state.go('livescore', {eventId: vm.selectedEvent});
      }


    };

    vm.loadAllEvents = function () {
      var deferred = $q.defer();

      var events = vm.db.child('events');

      vm.allEvents = $firebaseArray(events);
      vm.allEvents.$loaded().then(function () {

        vm.allEvents = vm.allEvents.filter(function (event) {
          return !event.hidden;
        });

        vm.allEvents.sort(function (e1, e2) {
          return e2.year - e1.year;
        });

        deferred.resolve();
      });

      return deferred.promise;
    };

    vm.loadAllEvents();


  });
