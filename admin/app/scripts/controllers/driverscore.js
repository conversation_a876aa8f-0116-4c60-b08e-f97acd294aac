'use strict';

/**
 * @ngdoc function
 * @name adminApp.controller:DriverscoreCtrl
 * @description
 * # DriverscoreCtrl
 * Controller of the adminApp
 */
angular.module('adminApp')
  .controller('DriverscoreCtrl', function ($state, $firebaseArray, $firebaseObject, $q, $mdToast, scorehelper) {

    var vm = this;

    vm.db = firebase.database().ref();

    vm.loadDrivers = function () {
      var deferred = $q.defer();

      var drivers = vm.db.child('drivers');

      vm.allDrivers = $firebaseArray(drivers);
      vm.allDrivers.$loaded().then(function () {
        deferred.resolve(vm.allDrivers);
      });

      return deferred.promise;
    };

    vm.loadDriversAtEvent = function() {

      vm.assignedDrivers = [];

      vm.driversInEvent = $firebaseArray(vm.event.$ref().child('drivers'));

      vm.driversInEvent.$loaded().then(function() {
        _.each(vm.driversInEvent,function (driver) {

          var driverId = driver.driverId;

          var driverToAdd = {
            assignedDriver: driver
          };

          _.each(vm.allDrivers, function (driverRef) {
            if (driverRef.$id === driverId) {
              driverToAdd.driverRef = driverRef;
            }
          });

          vm.assignedDrivers.push(driverToAdd);

        });

        // seperate firstround and final round


        vm.firstRoundDrivers = vm.assignedDrivers;



        scorehelper.getSortedScoreForRound(vm.event, 1).then(function (sorted) {

          var sorting = [];
          _.each(sorted, function (d) {

            var driverSorted = vm.firstRoundDrivers.find(function (driver)  {
              var tester =  driver.assignedDriver.driverId === d.driver.driverId;
              return tester;
            });

            sorting.push(driverSorted);

          });

          vm.firstRoundDrivers = sorting;

        });


        scorehelper.getSortedScoreForRound(vm.event,2).then(function (sorted) {

          vm.secondRoundDrivers = [];

          _.each(sorted, function(driver) {
            vm.secondRoundDrivers.push(vm.firstRoundDrivers.find(function (first) {
              return first.assignedDriver.driverId === driver.driver.driverId;
            }));
          });

        });

      });


    };

    vm.getEvent = function (id) {
      var deferred = $q.defer();

      var event = $firebaseObject(vm.db.child('events').child(id));
      vm.event = event;

      event.$loaded().then(function () {
        deferred.resolve(event);

        vm.sectionsQualification = [];

        for (var i = 0; i < event.sectionCountQualification; i++) {
          vm.sectionsQualification.push(i+1);
        }

        vm.sectionsFinal = [];
        for (var i = 0; i < event.sectionCountFinal; i++) {
          vm.sectionsFinal.push(i+1);
        }

        vm.loadDriversAtEvent();

      });

      return deferred.promise;
    };

    vm.sectionScoreChanged = function(driver) {
        for (var i = 0; i < vm.driversInEvent.length; i++) {
          if (
            vm.driversInEvent[i].active &&
            vm.driversInEvent[i].driverId !== driver.assignedDriver.driverId
          ) {
            vm.driversInEvent[i].active = false;
            vm.driversInEvent.$save(vm.driversInEvent[i]);
          }
        }

      vm.driversInEvent.$save(driver.assignedDriver).then(function () {
        $mdToast.show(
          $mdToast.simple()
            .textContent('Sektion aktualisiert!')
            .hideDelay(3000)
        );
      });


    };

    vm.tabSelected = function(tab) {
      if (tab === 2) {
        vm.loadDrivers().then(function () {
          vm.getEvent($state.params.id);
        });
      }
    }

    vm.loadDrivers().then(function () {
      vm.getEvent($state.params.id);
    });

    vm.getClass = function(driver) {
      debugger;
      if (driver.assignedDriver.active) {
        return "active";
      } else return "";
    }




  });
