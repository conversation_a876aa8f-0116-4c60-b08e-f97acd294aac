'use strict';

/**
 * @ngdoc function
 * @name adminApp.controller:LoginCtrl
 * @description
 * # LoginCtrl
 * Controller of the adminApp
 */
angular.module('adminApp')

  .config(function ($stateProvider) {

    $stateProvider
      .state('login', {
        url: "/login",
        templateUrl: "views/login.html"
      });
  })

  .controller('LoginCtrl', function ($firebaseAuth, $state) {


    var vm = this;

    vm.login = function () {


      $firebaseAuth().$signInWithEmailAndPassword(vm.user.email, vm.user.password).then(function (firebaseUser) {

        $state.go("admin", {});

      }).catch(function (error) {
        console.error("Authentication failed:", error);
      });


    }


  });
