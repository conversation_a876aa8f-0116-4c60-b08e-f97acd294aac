'use strict';

/**
 * @ngdoc function
 * @name adminApp.controller:PrintCtrl
 * @description
 * # PrintCtrl
 * Controller of the adminApp
 */
angular.module('adminApp')
  .config(function ($stateProvider) {


    $stateProvider
      .state('print', {
        url: "/print",
        templateUrl: "views/print/index.html",
        controller: function ($state) {
          if ($state.current.name === 'print') {
            $state.go('print.events');
          }
        }
      })

      .state('print.event', {
        url: "/event/:eventId",
        templateUrl: "views/print/event.html"
      })

      .state('print.events', {
        url: "/events",
        templateUrl: "views/print/eventSelector.html"
      });

  })

  .controller('PrintCtrl', function ($rootScope, $scope, $state, $firebaseObject, $firebaseArray, $q, scorehelper, image) {

    var vm = this;

    vm.db = firebase.database().ref();

    vm.eventId = $state.params.eventId;

    $scope.mode = 'print';

    vm.printMe = function () {


      var fontSize = 20;
      var headerFontSize = 30;
      if (vm.event && vm.event.preQualified === 5) {
        fontSize = 18;
        headerFontSize = 25;
      }


      var docDefinition = {
        pageSize: 'A4',
        pageOrientation: 'landscape',
        pageMargins: [5, 5, 5, 5],

        styles: {
          header: {
            fontSize: headerFontSize,
            bold: true,
            margin: [5, 10, 0, 0]
          },
          h1: {
            fontSize: fontSize,
            bold: true,
            margin: [0, 5, 0, 5]
          },
          ranking: {},
          row: {
            fontSize: fontSize
          },
          highlight: {
            fillColor: '#d3d7d6'
          },
          icon: {
            height: 20
          },
          driver: {
            margin: [5, 0, 100, 0]
          },
          section: {},
          pointCount: {},
          timeSection: {
            fontSize: 15
          }
        },

        content: [
          {
            columns: [
              {
                // auto-sized columns have their widths based on their content
                width: 60,
                image: image.adacImage
              },
              {
                // star-sized columns fill the remaining space
                // if there's more than one star-column, available width is divided equally
                style: 'header',
                text: ('Ergebnisse ' + vm.event.name)
              },
              {
                // percentage width
                width: 130,
                image: image.ilmbergerImage
              }
            ],
            // optional space between columns
            columnGap: 10
          }
        ]
      };

      docDefinition.content.push(
        {
          text: 'Qualifikation',
          style: 'h1'
        });


      $q.all([scorehelper.getSortedScoreForRound(vm.event, 1), scorehelper.getSortedScoreForRound(vm.event, 2)]).then(function (results) {


        var scoreFirstRound = results[0];
        var scoreSecondRound = results[1];


        // map drivers

        scoreFirstRound.map(function (s) {
          s.driverRef = vm.allDrivers.find(function (d) {
            return s.driver.driverId === d.$id;
          });
        });

        scoreSecondRound.map(function (s) {
          s.driverRef = vm.allDrivers.find(function (d) {
            return s.driver.driverId === d.$id;
          });
        });


        var rows = [];

        var shouldHighlight = false;

        var table1 = {
          widths: ['auto', 'auto', '45%'],
          body: []
        };

        var qualiSections = vm.event.qualificationDisplayOrder.split(',').map(function (v) {
          table1.widths.push('*');
          return parseInt(v);
        });

        // gesamtpunkte
        table1.widths.push('*');

        _.each(scoreFirstRound, function (score, index, list) {

          var icon;

          if (index < vm.event.preQualified) {
            icon = image.checkImage;
          }

          if (index === vm.event.preQualified || index === (vm.event.preQualified + 1)) {
            if (score.driver.suddenDeathQualified) {
              icon = image.fireGreen
            } else {
              icon = image.fireYellow
            }
          }
          if (index > (vm.event.preQualified + 1)) {
            icon = image.xImage;
          }

          var columns = [{
            style: ['ranking', 'row'],
            text: ((index + 1) + '.')

          }, {
            style: ['icon', 'row'],
            width: 20,
            height: 20,
            image: icon
          }, {
            style: ['driver', 'row'],
            text: (score.driver.driverNumber + ' - ' + score.driverRef.firstname + ' ' + score.driverRef.lastname)
          }];

          _.each(qualiSections, function (section) {

            var sectionScore;
            if (score.driver.scores && score.driver.scores[1]) {
              sectionScore = score.driver.scores[1][section];
            }

            if (sectionScore == undefined) {
              sectionScore = '-';
            }
            columns.push({
              style: ['section', 'row'],
              text: sectionScore,
              alignment: 'center'
            });
            if (section == 10) {
              columns[columns.length - 1].style.push('timeSection');
            }

          });

          columns.push({
            style: ['pointCount', 'row'],
            //width: '*',
            text: score.pointCount
          });

          rows.push(columns);
          if (shouldHighlight) {
            _.each(columns, function (column) {
              column.style.push('highlight');
            });
            shouldHighlight = false;
          } else {
            shouldHighlight = true;
          }


        });

        table1.body = rows;


        docDefinition.content.push({
          table: table1,
          layout: 'noBorders'
        });


        docDefinition.content.push(
          {
            text: 'Finale',
            style: 'h1'
          });

        var table2 = {
          widths: ['auto', 'auto', '45%'],
          body: []
        };

        var finalSections = vm.event.finalDisplayOrder.split(',').map(function (v) {
          table2.widths.push('*');
          return parseInt(v);
        });

        // gesamtpunkte
        table2.widths.push('*');

        var row2 = [];

        _.each(scoreSecondRound, function (score, index, list) {

          var columns = [{
            style: ['ranking', 'row'],
            text: ((index + 1) + '.')

          }, {
            style: ['icon', 'row'],
            text: '',
            width: 20
          }, {
            style: ['driver', 'row'],
            text: (score.driver.driverNumber + ' - ' + score.driverRef.firstname + ' ' + score.driverRef.lastname)
          }];


          _.each(finalSections, function (section) {

            var sectionScore;
            if (score.driver.scores && score.driver.scores[2]) {
              sectionScore = score.driver.scores[2][section];
            }

            if (sectionScore == undefined) {
              sectionScore = '-';
            }
            columns.push({
              style: ['section', 'row'],
              text: sectionScore,
              alignment: 'center'
            });

            if (section == 10) {
              columns[columns.length - 1].style.push('timeSection');
            }

          });

          columns.push({
            style: ['pointCount', 'row'],
            text: score.pointCount
          });

          row2.push(columns);

          if (shouldHighlight) {
            _.each(columns, function (column) {
              column.style.push('highlight');
            });
            shouldHighlight = false;
          } else {
            shouldHighlight = true;
          }


        });

        table2.body = row2;


        docDefinition.content.push({
          table: table2,
          layout: 'noBorders'
        });


        pdfMake.createPdf(docDefinition).open();


      });


    };

    vm.loadDrivers = function () {
      var deferred = $q.defer();

      var drivers = vm.db.child('drivers');

      vm.allDrivers = $firebaseArray(drivers);
      vm.allDrivers.$loaded().then(function () {
        deferred.resolve(vm.allDrivers);
      });

      return deferred.promise;
    };

    vm.loadEvent = function (id) {
      var deferred = $q.defer();

      if (!id) {
        deferred.reject();
      } else {
        vm.event = $firebaseObject(vm.db.child('events').child(id));

        vm.event.$loaded().then(function () {
          deferred.resolve(vm.event);
        });

      }

      return deferred.promise;
    };


    $rootScope.$on('$stateChangeSuccess', function (event, toState, toParams, fromState, fromParams) {
      if (toState.name === 'print.event') {
        vm.loadEvent($state.params.eventId);
      }
    });

    if ($state.current.name === 'print.event') {
      vm.loadEvent($state.params.eventId);
    }

    vm.loadDrivers();


  });
