'use strict';

/**
 * @ngdoc function
 * @name adminApp.controller:DriverlistCtrl
 * @description
 * # DriverlistCtrl
 * Controller of the adminApp
 */
angular.module('adminApp')
  .controller('DriverlistCtrl', function ($scope, $state, $firebaseArray, $firebaseObject, $q, $mdToast) {


    var vm = this;

    vm.db = firebase.database().ref();

    //vm.assignedDrivers = [];

    vm.loadEvents = function () {

      var deferred = $q.defer();

      var events = vm.db.child('events');

      vm.events = $firebaseArray(events);
      vm.events.$loaded().then(function () {
        deferred.resolve();
      });

      return deferred.promise;

    };

    vm.loadDrivers = function () {
      var deferred = $q.defer();

      var drivers = vm.db.child('drivers');

      vm.allDrivers = $firebaseArray(drivers);
      vm.allDrivers.$loaded().then(function () {
        deferred.resolve(vm.allDrivers);
      });

      return deferred.promise;
    };

    vm.loadDriversAtEvent = function() {

      var deferred = $q.defer();

      vm.assignedDrivers = [];

      vm.driversInEvent = $firebaseArray(vm.event.$ref().child('drivers'));

      vm.driversInEvent.$loaded().then(function() {

        _.each(vm.driversInEvent,function (driver) {
          var driverId = driver.driverId;

          var driverToAdd = {
            assignedDriver: driver
          };

          _.each(vm.allDrivers, function (driverRef) {
            if (driverRef.$id === driverId) {
              driverToAdd.driverRef = driverRef;
            }
          });

          vm.assignedDrivers.push(driverToAdd);

        });


        deferred.resolve();

      });

      return deferred.promise;


    };

    vm.getEvent = function (id) {
      var deferred = $q.defer();

      var event = $firebaseObject(vm.db.child('events').child(id));
      vm.event = event;

      event.$loaded().then(function () {

        vm.loadDriversAtEvent().then(function() {
          deferred.resolve(event);
        });

      });

      return deferred.promise;
    };

    vm.removeDriverFromEvent = function(driverToRemove) {

      vm.driversInEvent.$remove(driverToRemove.assignedDriver).then(function () {
        $mdToast.show(
          $mdToast.simple()
            .textContent('Fahrer entfernt!')
            .hideDelay(3000)
        );
      });

    };

    vm.addDriverToEvent = function (driverToAdd) {

      vm.event.$loaded().then(function (event) {

        var driversInEvent = $firebaseArray(event.$ref().child('drivers'));

        driversInEvent.$loaded().then(function () {

          var isAssignedAlready = false;
          _.each(driversInEvent, function (driver) {

            if (driver.driverId === driverToAdd.$id) {
              isAssignedAlready = true;
            }

          });

          if (!isAssignedAlready) {

            driversInEvent.$add({
              driverId: driverToAdd.$id,
              driverNumber: driversInEvent.length+1
            }).then(function () {
              $mdToast.show(
                $mdToast.simple()
                  .textContent('Fahrer hinzugefügt!')
                  .hideDelay(3000)
              );

            });
          } else {
            $mdToast.show(
              $mdToast.simple()
                .textContent('Fahrer ist schon im Event!')
                .hideDelay(3000)
            );
          }

        });


      });


    };

    vm.driverChanged = function (driver) {
      if (driver.assignedDriver.driverNumber) {
        // update
        var d = driver.assignedDriver;

        vm.driversInEvent.$save(d).then(function () {
          $mdToast.show(
            $mdToast.simple()
              .textContent('Fahrer Startnummer geändert!')
              .hideDelay(3000)
          );
        });

      }
    };


    vm.loadDrivers().then(function () {
      vm.getEvent($state.params.id).then(function () {


      });
    });




  });
