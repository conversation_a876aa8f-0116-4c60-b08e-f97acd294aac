'use strict';

/**
 * @ngdoc function
 * @name adminApp.controller:LivescoreCtrl
 * @description
 * # LivescoreCtrl
 * Controller of the adminApp
 */
angular.module('adminApp')

  .config(function ($stateProvider) {

    $stateProvider
      .state('livescore', {
        url: "/livescore/:eventId",
        templateUrl: "views/livescores.html"
      });
  })

  .controller('LivescoreCtrl', function ($scope, $rootScope, $state, $q, $firebaseObject, $firebaseArray, scorehelper, $mdDialog) {

    var vm = this;
    vm.db = firebase.database().ref();

    vm.showDefaultMain = false;

    vm.flip = false;
    vm.autoChanged = false;
    vm.secondRoundCountLast = 0;


    vm.loadDriver = function (driverId) {
      var deferred = $q.defer();

      if (!driverId) {
        deferred.reject();
      } else {
        var driver = $firebaseObject(vm.db.child('drivers').child(driverId));

        driver.$loaded().then(function () {
          deferred.resolve(driver);
        });

      }

      return deferred.promise;
    };

    vm.loadAllDrivers = function () {

      var deferred = $q.defer();

      var drivers = vm.db.child('drivers');

      var allDrivers = $firebaseArray(drivers);
      allDrivers.$loaded().then(function () {
        deferred.resolve(allDrivers);
      });

      return deferred.promise;

    };


    /**
     * Load event with promise.
     * @param id
     */
    vm.loadEvent = function (id) {
      var deferred = $q.defer();

      if (!id) {
        deferred.reject();
      } else {
        var event = $firebaseObject(vm.db.child('events').child(id));

        event.$loaded().then(function () {
          deferred.resolve(event);
        });

      }

      return deferred.promise;
    };

    /**
     * Set round name based on round
     * Handle checkboxes
     * @param round
     */
    vm.setRound = function (round) {

      if (!round) {
        // figure out round based on scores.

        // for now default = 1;
        round = 1;
      }

      vm.round = round;

      switch (round) {
        case 1:
          vm.selectQualification = true;
          vm.selectFinal = false;
          vm.roundName = 'Qualifikation';
          vm.flip = false;
          break;

        case 2:
          vm.selectQualification = false;
          vm.selectFinal = true;
          vm.roundName = 'Finale';
          vm.flip = true;
          break;

        default:

      }

      vm.getScoresForRound(round).then(function (sortedScores) {

        vm.driverScores = sortedScores;

      });

    };

    vm.getScoresForRound = function (round) {

      if (!round) {
        round = 1;
      }

      var deferred = $q.defer();

      scorehelper.getSortedScoreForRound(vm.event, round).then(function (sortedScores) {
        deferred.resolve(sortedScores);
      });

      return deferred.promise;
    };

    /**
     * Event handler for manual round changes
     * @param round
     */
    vm.roundChanged = function (round) {
      vm.setRound(round);
    };

    /**
     *
     */
    vm.flipped = function () {
     vm.roundChanged(vm.flip ? 2 : 1);
    };


    vm.initEvent = function (eventId, round) {

      var deferred = $q.defer();

      if (!round) {
        round = 1;
      }

      vm.round = round;

      $q.all([vm.loadEvent(eventId), vm.loadAllDrivers()]).then(function (results) {

        vm.allDrivers = results[1];
        vm.event = results[0];


        if (!vm.event.mainImage) {
          vm.showDefaultMain = true;
        }

        _.each(results[0].drivers, function (driver) {
          driver.driverRef = vm.allDrivers.find(function (d) {
            return d.$id === driver.driverId;
          });
        });

        if (vm.event.qualificationDisplayOrder) {
          vm.firstRoundDisplayOrder = vm.event.qualificationDisplayOrder.split(',').map(function (v) {
            return parseInt(v);
          });
        }

        if (vm.event.finalDisplayOrder) {
          vm.secondRoundDisplayOrder = vm.event.finalDisplayOrder.split(',').map(function (v) {
            return parseInt(v);
          });
        }

        vm.whichRoundToShow().then(function (roundToSelect) {

          vm.round = roundToSelect;

          if (vm.round === 2) {
            vm.flip = true;
          } else {
            vm.flip = false;
          }

          vm.setRound(vm.round);

          deferred.resolve(vm.event);
        });

      });

      return deferred.promise;

    };

    vm.whichRoundToShow = function () {
      var deferred = $q.defer();

      vm.getScoresForRound(2).then(function (scores) {

        var round = 1;

        var secondRoundCount = 0;

        _.each(scores, function (score) {
          if (score.sectionCount > 0) {
           secondRoundCount++;
            round = 2;
          }
        });

        if (!vm.autoChanged) {
          vm.autoChanged = true;
          deferred.resolve(round);
        } else {
          if (round === 2 && vm.round === 1) {
            // alert something in the final changed.

            if (vm.secondRoundCountLast !== secondRoundCount) {
              var changedData = $mdDialog.confirm().title('Neue Daten im Finale.').ok('Wechseln').cancel('Bleiben');

              console.log('I could have asked to move');

              /*
              $mdDialog.show(changedData).then(function (res) {
                vm.setRound(2);
              });

               */
            }

          }
          deferred.resolve(vm.round);
        }

        vm.secondRoundCountLast = secondRoundCount;

      });

      return deferred.promise;
    };


    vm.getClass = function (index, driver) {
      var x = index % 2;
      var classes = "";
      if (x === 0) {
        classes += "row-0";
      } else {
        classes += "row-1";
      }

      if (driver.active) {
        classes += " active";
      }

      return classes;

    };

    vm.isDriverInSuddenDeath = function (index) {

      var min = vm.event.preQualified
      var max = vm.event.preQualified + vm.event.suddenDeth;

      if (index >= min && index < max) {
        return true;
      };
      return false;

    };

    vm.initEvent($state.params.eventId, $scope.round).then(function (event) {
      event.$watch(function () {
        vm.initEvent($state.params.eventId, vm.round);
      });
    });


  });
