'use strict';

/**
 * @ngdoc function
 * @name adminApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the adminApp
 */
angular.module('adminApp')
  .config(function ($stateProvider) {

    $stateProvider
      .state('admin', {
        url: "/admin",
        templateUrl: "views/admin/index.html"
      })
      .state('admin.events', {
        url: "/events",
        templateUrl: 'views/admin/events/events.html'
      })
      .state('admin.event', {
        url: "/event/:id",
        templateUrl: 'views/admin/events/event.html'
      })
      .state('admin.drivers', {
        url: "/drivers",
        templateUrl: 'views/admin/drivers/index.html'
      })
      .state('admin.drivers.list', {
        url: "/drivers",
        templateUrl: 'views/admin/drivers/drivers.html'
      })
      .state('admin.drivers.driver', {
        url: "/driver/:id",
        templateUrl: 'views/admin/drivers/driver.html'
      })

      .state('admin.drivers.add', {
        url: "/drivers/add",
        templateUrl: 'views/admin/drivers/adddriver.html'
      });
  })

  .controller('AdminCtrl', function ($rootScope, $location, $http, $firebaseAuth, $firebaseArray, $firebaseObject, $mdSidenav, $state, $mdDialog, $mdToast, $q, $filter) {

    var vm = this;

    vm.busy = false;

    vm.db = firebase.database().ref();

    var firebaseUser = $firebaseAuth().$getAuth();

    if (firebaseUser) {
    } else {

      $firebaseAuth().$onAuthStateChanged(function (firebaseUser) {
        if (firebaseUser) {
        } else {
          $location.path('login');
        }
      });

    }

    vm.logout = function () {
      $firebaseAuth().$signOut().then(function () {
        $state.go('login');
      });
    };

    vm.loadEvents = function () {

      vm.busy = true;

      var deferred = $q.defer();

      var events = vm.db.child('events');

      vm.events = $firebaseArray(events);
      vm.events.$loaded().then(function () {
        deferred.resolve();
        vm.busy = false;
      });


      return deferred.promise;

    };

    vm.loadDrivers = function () {

      vm.busy = true;

      var deferred = $q.defer();

      var drivers = vm.db.child('drivers');

      vm.drivers = $firebaseArray(drivers);
      vm.drivers.$loaded().then(function () {
        deferred.resolve();
        vm.busy = false;
      });


      return deferred.promise;

    };

    vm.addDriver = function () {

      vm.busy = true;

      vm.drivers.$add(vm.driver).then(function (driver) {

        var key = driver.key;

        vm.busy = false;

        $mdToast.show(
          $mdToast.simple()
            .textContent('Fahrer erstellt!')
            .hideDelay(3000)
        );

        $state.go('admin.drivers.driver', {
          id: driver.key
        });

      });
    };

    vm.updateDriver = function () {

      vm.busy = true;
      vm.drivers.$save(vm.driver).then(function (driver) {

        vm.busy = false;
        var key = driver.key;

        $mdToast.show(
          $mdToast.simple()
            .textContent('Fahrer aktualisiert!')
            .hideDelay(3000)
        );

        $state.go('admin.drivers.list', {
          id: driver.key
        });

      });
    };

    vm.deleteDriver = function (driver) {
      vm.drivers.$remove(driver).then(function () {
        $mdToast.show(
          $mdToast.simple()
            .textContent('Fahrer gelöscht!')
            .hideDelay(3000)
        );
      });
    };

    vm.addEvent = function (ev) {

      var confirm = $mdDialog.prompt()
        .title('Event?')
        .textContent('Name des Events.')
        .placeholder('ADAC Hallentrial 2017')
        .ariaLabel('ADAC Hallentrial 2017')
        .initialValue('ADAC Hallentrial 201')
        .targetEvent(ev)
        .ok('OK!')
        .cancel('Abbruch');

      $mdDialog.show(confirm).then(function (result) {
        vm.createEvent(result, 2017);
      }, function () {

      });

    };

    vm.createEvent = function (name, year) {

      vm.busy = true;

      vm.events.$add({
        name: name,
        year: year
      }).then(function (event) {

        vm.busy = false;

        var key = event.key;

        $mdToast.show(
          $mdToast.simple()
            .textContent('Event erstellt!')
            .hideDelay(3000)
        );

        $state.go('admin.event', {
          id: event.key
        });

      });

    };

    vm.updateEvent = function () {

      vm.busy = true;

      vm.event.$save().then(function () {
        vm.busy = false;
        $mdToast.show(
          $mdToast.simple()
            .textContent('Event aktualisiert!')
            .hideDelay(3000)
        );
      });

    };

    vm.printEvent = function () {
      var eventId = $state.params.id;

      $location.path('print/event/' + eventId);

    };

    vm.toDrivers = function () {
      vm.hideNavigation();
      $state.go('admin.drivers.list');
    };

    vm.toEvents = function () {
      vm.hideNavigation();
      $state.go('admin.events');

      vm.loadEvents();

    };

    vm.getDriver = function (id) {

      vm.busy = true;

      var deferred = $q.defer();

      vm.loadDrivers().then(function () {

        _.each(vm.drivers, function (driver) {
          if (driver.$id === id) {
            deferred.resolve(driver);
            vm.driver = driver;

            vm.busy = false;
          }
        });

      });

      return deferred.promise;
    };

    vm.getEvent = function (id) {

      vm.busy = true;

      var deferred = $q.defer();

      vm.loadEvents().then(function () {

        // loading the event here
        var event = $firebaseObject(vm.db.child('events').child(id));
        event.$loaded().then(function () {
          deferred.resolve(event);
          vm.event = event;
          vm.busy = false;
        });

      });

      return deferred.promise;
    };

    vm.addSponsoring = function (eventId) {

      vm.event.sponsoring = $firebaseArray(vm.event.$ref().child('sponsoring'));

      if (!vm.event.sponsoring) {
        vm.event.sponsoring = [];
      }
      vm.event.sponsoring.push({
        name: '',
        section: ''
      });
    };

    vm.saveSponsoring = function (sponsoring) {
      vm.updateEvent();
    };

    vm.deleteSponsoring = function (sponsoring) {
      vm.event.sponsoring = _.without(vm.event.sponsoring, sponsoring);

      vm.updateEvent();

    };

    vm.upload = function (file, sponsoring) {

      vm.busy = true;

      file.$$ngfDataUrlPromise.then(function (urlData) {
        sponsoring.imageDataURL = urlData;
        vm.busy = false;
      });

    };




    vm.saveMainSponsoring = function () {
      vm.updateEvent();
    };

    vm.deleteMainSponsoring = function (sponsoring) {
      vm.event.mainImage = null;
      vm.updateEvent();

    };

    vm.uploadMain = function (file) {

      vm.busy = true;

      file.$$ngfDataUrlPromise.then(function (urlData) {

        vm.event.mainImage = urlData;
        //sponsoring.imageDataURL = urlData;
        vm.busy = false;
      });

    };



    vm.saveSecondarySponsoring = function () {
      vm.updateEvent();
    };

    vm.deleteSecondarySponsoring = function (sponsoring) {
      vm.event.secondaryImage = null;
      vm.updateEvent();

    };

    vm.uploadSecondary = function (file) {

      vm.busy = true;

      file.$$ngfDataUrlPromise.then(function (urlData) {
        vm.event.secondaryImage = urlData;
        vm.busy = false;
      });

    };




    vm.range = function (n) {
      return new Array(n);
    };

    vm.showNavigation = function () {
      $mdSidenav('left').open();
    };

    vm.hideNavigation = function () {
      $mdSidenav('left').close();
    };


    vm.resetScore = function (event) {

      _.each(event.drivers, function (driver) {
        delete driver.scores;
      });

      vm.busy = true;

      event.$save().then(function () {
        vm.busy = false;
        $mdToast.show(
          $mdToast.simple()
            .textContent('Resultate Zurückgesetzt!')
            .hideDelay(3000)
        );
      });

    };


    if ($state.current.name === 'admin.event') {

      vm.getEvent($state.params.id);

    }
    if ($state.current.name === 'admin.drivers.driver') {

      vm.getDriver($state.params.id);

    }

    vm.loadDrivers();
    vm.loadEvents();


    $rootScope.$on('$stateChangeSuccess', function (event, toState, toParams, fromState, fromParams) {
      if (toState.name === 'admin.event') {
        vm.getEvent($state.params.id);
      } else if (toState.name === 'admin.drivers.driver') {
        vm.getDriver($state.params.id);
      }
    });


  });
