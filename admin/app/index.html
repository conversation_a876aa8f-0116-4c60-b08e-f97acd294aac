<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <title></title>
    <meta name="description" content="">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- Place favicon.ico and apple-touch-icon.png in the root directory -->
    <!-- build:css(.) styles/vendor.css -->
    <!-- bower:css -->
    <link rel="stylesheet" href="bower_components/angular-material/angular-material.css" />
    <link rel="stylesheet" href="bower_components/angular-material-data-table/dist/md-data-table.css" />
    <!-- endbower -->
    <!-- endbuild -->
    <!-- build:css(.tmp) styles/main.css -->
    <link rel="stylesheet" href="styles/main.css" media="screen">
    <!-- endbuild -->




  </head>
  <body ng-app="adminApp">
    <!--[if lte IE 8]>
      <p class="browsehappy">You are using an <strong>outdated</strong> browser. Please <a href="http://browsehappy.com/">upgrade your browser</a> to improve your experience.</p>
    <![endif]-->

    <div flex layout-fill ui-view=""></div>


    <!-- build:js(.) scripts/vendor.js -->
    <!-- bower:js -->
    <script src="bower_components/jquery/dist/jquery.js"></script>
    <script src="bower_components/angular/angular.js"></script>
    <script src="bower_components/angular-animate/angular-animate.js"></script>
    <script src="bower_components/angular-cookies/angular-cookies.js"></script>
    <script src="bower_components/angular-resource/angular-resource.js"></script>
    <script src="bower_components/angular-route/angular-route.js"></script>
    <script src="bower_components/angular-sanitize/angular-sanitize.js"></script>
    <script src="bower_components/angular-touch/angular-touch.js"></script>
    <script src="bower_components/angular-aria/angular-aria.js"></script>
    <script src="bower_components/angular-messages/angular-messages.js"></script>
    <script src="bower_components/angular-material/angular-material.js"></script>
    <script src="bower_components/angular-ui-router/release/angular-ui-router.js"></script>
    <script src="bower_components/firebase/firebase.js"></script>
    <script src="bower_components/angularfire/dist/angularfire.js"></script>
    <script src="bower_components/angular-material-data-table/dist/md-data-table.js"></script>
    <script src="bower_components/underscore/underscore-umd.js"></script>
    <script src="bower_components/html2canvas/build/html2canvas.js"></script>
    <script src="bower_components/pdfmake/build/pdfmake.js"></script>
    <script src="bower_components/pdfmake/build/vfs_fonts.js"></script>
    <script src="bower_components/ng-file-upload/ng-file-upload.js"></script>
    <script src="bower_components/bootstrap-sass/assets/javascripts/bootstrap.js"></script>
    <!-- endbower -->
    <!-- endbuild -->

        <!-- build:js({.tmp,app}) scripts/scripts.js -->
        <script src="scripts/app.js"></script>
        <script src="scripts/controllers/admin.js"></script>
        <script src="scripts/controllers/livescore.js"></script>
        <script src="scripts/controllers/login.js"></script>
        <script src="scripts/controllers/driverlist.js"></script>
        <script src="scripts/controllers/driverscore.js"></script>
        <script src="scripts/controllers/eventselector.js"></script>
        <script src="scripts/directives/livescoreview.js"></script>
        <script src="scripts/controllers/print.js"></script>
        <script src="scripts/services/scorehelper.js"></script>
        <script src="scripts/services/image.js"></script>
        <script src="scripts/controllers/livescoreselector.js"></script>
        <!-- endbuild -->
</body>
</html>
