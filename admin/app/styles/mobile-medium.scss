@media screen and (max-width: 959px) and (min-width: 600px) {
  $live-header-height: 70px;
  $live-header-font-size: 25px !important;
  $live-header-text-margin: 10px;

  $row-font-size: 17px;

  $position-margin: 0 0 0 5px;
  $position-width: 20px;

  $active-border: 2px solid black;

  .live-header {
    height: $live-header-height;
    max-height: $live-header-height;
    font-size: $live-header-font-size;

    background-color: white;
    color: black;
  }

  .title-text {
    padding-left: $live-header-text-margin;
  }

  .live-logo {
    height: inherit;
    width: auto;
  }

  .position {
    margin: $position-margin;
    min-width: $position-width;
  }

  .row-0 {
    font-size: $row-font-size;
    background-color: #d3d7d6;
    text-align: center;
    color: black;
  }

  .row-1 {
    font-size: $row-font-size;
    text-align: center;
    background-color: white;
    color: black;
  }

  .active {
    border-top: $active-border;
    border-bottom: $active-border;
  }

  .driver-name-wrapper {
    text-align: left;
    min-width: 200px;
  }

  .driver-name {
    min-width: 30px;
    text-align: start;
  }

  .icon-div {
    margin: 10px 10px;
  }
}
