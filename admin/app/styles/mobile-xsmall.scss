@media screen and (max-width: 359px) {

  $live-header-height: 60px;
  $live-header-font-size: 13px !important;
  $live-header-text-margin: 10px 0 0 10px;

  $row-font-size: 14px;

  $position-margin: 0 0 0 5px;
  $position-width: 20px;

  $active-border: 2px solid black;

  .live-header {
    height: $live-header-height;
    max-height: $live-header-height;
    font-size: $live-header-font-size;
    background-color: white;
    color: black;
  }

  .title-text {
    padding-left: $live-header-text-margin;
  }

  .live-logo {
    //height: inherit;
    //width: auto;
    max-width: 100px;
    height: auto;
  }

  .position {
    margin: $position-margin;
    min-width: $position-width;
  }

  .row-0 {
    font-size: $row-font-size;
    background-color: #d3d7d6;
    text-align: center;
    color: black;
  }

  .row-1 {
    font-size: $row-font-size;
    text-align: center;
    color: black;
    background-color: white;
  }

  .active {
    border-top: $active-border;
    border-bottom: $active-border;
  }

  .driver-name-wrapper {
    text-align: left;
    min-width: 160px;
  }

  .driver-name {
    min-width: 20px;
    text-align: start
  }

  .icon-div {
    margin: 0px 5px 0px 5px;
  }
}
