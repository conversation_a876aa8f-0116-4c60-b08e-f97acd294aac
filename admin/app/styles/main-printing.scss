$icon-font-path: "../bower_components/bootstrap-sass-official/assets/fonts/bootstrap/";
// bower:scss
@import "bootstrap-sass/assets/stylesheets/_bootstrap.scss";
// endbower

.browsehappy {
  margin: 0.2em 0;
  background: #ccc;
  color: #000;
  padding: 0.2em 0;
}

body {
  padding: 0;
}

h1 {
  font-size: 30px;
}

$default-padding: 25px;

.login-card {
  padding: $default-padding;
}

.event-card {
  padding: $default-padding;
}

.driver-card {
  padding: $default-padding;
}

.section-score {
  margin: 18px 0 !important;
  padding: 0 !important;
}

.icon-div {
  margin: 0px 10px 0px 0px;
}

.timeSection {
  font-size: smaller;
}

.icon-qualified {
  color: green;
}

.icon-suddendeath {
  color: #fed02f;
}

.icon-suddendeath-qualified {
  color: green;
}

.icon-not-qualified {
  color: red;
}

.print-header {
  height: 75px;
}

$live-header-height: 100px;
$live-header-font-size: 50px !important;
$live-header-text-margin: 50px;
$row-font-size: 24px;

$position-margin: 0 0 0 10px;
$position-width: 80px;

$active-border: 4px solid black;

.live-header {
  height: $live-header-height;
  max-height: $live-header-height;
  font-size: $live-header-font-size;
  background-color: white;
  color: black;
}

.title-text {
  padding-left: $live-header-text-margin;
}

.live-logo {
  height: inherit;
  width: auto;
}

.position {
  margin: $position-margin;
  width: $position-width;
}

.row-0 {
  font-size: $row-font-size;
  background-color: #d3d7d6;
  text-align: center;
}

.row-1 {
  font-size: $row-font-size;
  text-align: center;
}

.active {
  //border-top: $active-border;
  //border-bottom: $active-border;
  background-color: #fed02f;
}



