$icon-font-path: "../bower_components/bootstrap-sass/assets/fonts/bootstrap/";
// bower:scss
@import "bootstrap-sass/assets/stylesheets/_bootstrap.scss";
// endbower

.browsehappy {
  margin: 0.2em 0;
  background: #ccc;
  color: #000;
  padding: 0.2em 0;
}

body {
  padding: 0;
}

$default-padding: 25px;

.login-card {
  padding: $default-padding;
}

.event-card {
  padding: $default-padding;
}

.driver-card {
  padding: $default-padding;
}

.section-score {
  margin: 18px 0 !important;
  padding: 0 !important;
}

.icon-div {
  margin: 10px 25px 0px 0px;
}

.timeSection {
  font-size: smaller;
}

.icon-qualified {
  color: green;
}

.icon-suddendeath {
  color: #fed02f;
}

.icon-suddendeath-qualified {
  color: green;
}

.icon-not-qualified {
  color: red;
}

.print-header {
  height: 75px;
}

.sponsoringImage {
  //width: 75px;
  //height:auto;
}

@media screen and (min-device-width: 1280px) {
  $live-header-height: 100px;
  $live-header-font-size: 50px !important;
  $live-header-text-margin: 50px;
  $row-font-size: 40px;

  $position-margin: 0 0 0 10px;
  $position-width: 80px;

  $active-border: 4px solid black;

  .live-header {
    height: $live-header-height;
    max-height: $live-header-height;
    font-size: $live-header-font-size;
    background-color: black;
    color: white;
    border-bottom: 3px solid #ffffff;
  }

  .title-text {
    padding-left: $live-header-text-margin;
  }

  .live-logo {
    height: 90%;
    width: auto;
  }

  .live-logo-yellow {
    margin-top: 15px;
    padding-bottom: 15px;
  }

  .position {
    margin: $position-margin;
    min-width: $position-width;
  }

  .row-0 {
    font-size: $row-font-size;
    text-align: center;
    background-color: black;
    color: white;
    border-bottom: 3px solid #ffffff;
  }

  .row-1 {
    font-size: $row-font-size;
    text-align: center;
    background-color: black;
    color: white;
    border-bottom: 3px solid #ffffff;
  }

  .active {
    //border-top: $active-border;
    //border-bottom: $active-border;
    background-color: #fed02f;
    color: black;
  }

  .driver-name-wrapper {
    text-align: left;
    min-width: 500px;
  }

  .driver-name {
    min-width: 60px;
    text-align: start;
  }

  .icon-div {
    margin: 10px 25px 0px 0px;
  }
}

@import "mobile-large";
@import "mobile-medium";
@import "mobile-medium-small";
@import "mobile-small";
@import "mobile-xsmall";
