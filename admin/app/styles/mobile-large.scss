@media screen and (max-width: 1279px) and (min-width: 960px) {
  $live-header-height: 75px;
  $live-header-font-size: 30px !important;
  $live-header-text-margin: 20px;

  $row-font-size: 30px;

  $position-margin: 0 0 0 5px;
  $position-width: 50px;

  $active-border: 4px solid black;

  .live-header {
    height: $live-header-height;
    max-height: $live-header-height;
    font-size: $live-header-font-size;
    background-color: black;
    color: white;
  }

  .title-text {
    padding-left: $live-header-text-margin;
  }

  .live-logo {
    height: 90%;
    width: auto;
  }

  .position {
    margin: $position-margin;
    min-width: $position-width;
  }

  .row-0 {
    font-size: $row-font-size;
    text-align: center;
    background-color: black;
    color: white;
  }

  .row-1 {
    font-size: $row-font-size;
    text-align: center;
    background-color: black;
    color: white;
  }

  .active {
    //border-top: $active-border;
    //border-bottom: $active-border;
    background-color: #fed02f;
    color: black;
  }

  .driver-name-wrapper {
    text-align: left;
    min-width: 370px;
  }

  .driver-name {
    min-width: 60px;
    text-align: start;
  }

  .icon-div {
    margin: 10px 25px 0px 10px;
  }
}
