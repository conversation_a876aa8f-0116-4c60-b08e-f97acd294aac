<md-content layout-fill flex layout="column" ng-controller="DriverscoreCtrl as vm">




  <md-tabs flex md-dynamic-height="true" >

    <md-tab label="Qualifikation" md-on-select="vm.tabSelected(1)">
      <md-content flex>

        <md-list flex>

          <md-list-item ng-repeat="driver in vm.firstRoundDrivers" class="{{vm.getClass(driver)}}" >

            <md-checkbox aria-label="Aktiver Fahrer" ng-change="vm.sectionScoreChanged(driver)" ng-model="driver.assignedDriver.active"></md-checkbox>

            <md-subheader>
              {{driver.assignedDriver.driverNumber + ' - ' + driver.driverRef.firstname + ' ' + driver.driverRef.lastname}}
            </md-subheader>

            <md-input-container class="section-score" ng-repeat="section in vm.sectionsQualification">
              <label>Sektion {{section}}</label>
              <input ng-model="driver.assignedDriver.scores[1][section]" type="number" ng-change="vm.sectionScoreChanged(driver)" ng-model-options='{ debounce: 500 }'>
            </md-input-container>

            <md-input-container>
              <label>Zeit</label>
              <input ng-model="driver.assignedDriver.scores[1][10]" type="number" ng-change="vm.sectionScoreChanged(driver)" ng-model-options='{ debounce: 500 }'>
            </md-input-container>

            <md-input-container>
              <label>SDQ</label>
              <md-checkbox aria-label="Fahrer durch Sudden Death qualifiziert" ng-change="vm.sectionScoreChanged(driver)" ng-model="driver.assignedDriver.suddenDeathQualified" ng-model-options='{ debounce: 500 }'></md-checkbox>
            </md-input-container>

            <md-divider ng-if="!$last"></md-divider>

          </md-list-item>
          <div>
            TEST
          </div>
          <div>
            TEST
          </div>

        </md-list>

      </md-content>
    </md-tab>

    <md-tab label="Finale" md-on-select="vm.tabSelected(2)">
      <md-content flex>

        <md-list flex>

          <md-list-item ng-repeat="driver in vm.secondRoundDrivers" class="{{vm.getClass(driver)}}" >

            <md-checkbox aria-label="Aktiver Fahrer" ng-change="vm.sectionScoreChanged(driver)" ng-model="driver.assignedDriver.active"></md-checkbox>

            <md-subheader>{{driver.assignedDriver.driverNumber + ' - ' + driver.driverRef.firstname + ' ' +
              driver.driverRef.lastname}}
            </md-subheader>

            <div layout="column">
              <div layout="row">
                <md-input-container class="section-score" ng-repeat="section in vm.sectionsFinal">
                  <label>Sektion {{section}}</label>
                  <input ng-model="driver.assignedDriver.scores[2][section]" type="number" ng-change="vm.sectionScoreChanged(driver)" ng-model-options='{ debounce: 500 }'>
                </md-input-container>

                <md-input-container>
                  <label>Zeit</label>
                  <input ng-model="driver.assignedDriver.scores[2][10]" type="number" ng-change="vm.sectionScoreChanged(driver)" ng-model-options='{ debounce: 500 }'>
                </md-input-container>

              </div>
            </div>

            <md-divider ng-if="!$last"></md-divider>
          </md-list-item>

          <div>
            TEST
          </div>
          <div>
            TEST
          </div>

        </md-list>

      </md-content>
    </md-tab>

  </md-tabs>


</md-content>



