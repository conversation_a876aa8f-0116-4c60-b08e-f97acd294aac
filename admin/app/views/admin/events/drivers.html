<md-content layout-fill flex ng-controller="DriverlistCtrl as vm" layout="row" ng-cloak>


  <md-card flex>
    <span class="md-headline">Fahrer im Event</span>

    <md-list>
      <md-list-item ng-repeat="driver in vm.assignedDrivers | orderBy:'assignedDriver.driverNumber'">

        <p>{{driver.driverRef.firstname}} - {{driver.driverRef.lastname}}</p>
        <md-input-container>
          <label>Fahrernummer</label>
          <input ng-change="vm.driverChanged(driver)" ng-model-options='{ debounce: 500 }'
                 ng-model="driver.assignedDriver.driverNumber" type="text" required>
        </md-input-container>

        <md-button ng-click="vm.removeDriverFromEvent(driver)" class="md-icon-button md-warn">
          <md-icon>delete</md-icon>
          <md-tooltip>Fahrer von Event entfernen</md-tooltip>
        </md-button>

        <md-divider ng-if="!$last"></md-divider>
      </md-list-item>
    </md-list>
  </md-card>


  <md-card flex>

    <span class="md-headline">Alle verfügbaren Fahrer</span>

    <md-list>
      <md-list-item ng-repeat="driver in vm.allDrivers">
        <p>{{driver.firstname}} - {{driver.lastname}}</p>
        <md-button ng-click="vm.addDriverToEvent(driver)" class="md-icon-button">
          <md-icon>add</md-icon>
          <md-tooltip>Fahrer zu Event hinzufügen</md-tooltip>
        </md-button>
        <md-divider ng-if="!$last"></md-divider>
      </md-list-item>
    </md-list>
  </md-card>


</md-content>
