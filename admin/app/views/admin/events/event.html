<md-content layout-fill layout="column" flex ng-cloak>
  <md-progress-linear md-mode="vm.busyModel" ng-disabled="!vm.busy" class="md-accent"></md-progress-linear>

  <md-tabs md-selected="0" md-border-bottom md-autoselect flex layout-fill>

    <md-tab>

      <md-tab-label>Event Bearbeiten</md-tab-label>

      <md-tab-body layout="column">

        <div layout="row">

          <md-card class="login-card">

            <md-card-title-text>
              <span class="md-headline">Event {{vm.event.name}}</span>
            </md-card-title-text>

            <span style="padding-top: 20px;"></span>

            <form name="eventForm" novalidate layout="column">

              <div layout="row" flex>
                <md-input-container>
                  <label>Name</label>
                  <input ng-model="vm.event.name" type="text" required>
                </md-input-container>

                <md-input-container>
                  <label>Jahr</label>
                  <input ng-model="vm.event.year" type="text">
                </md-input-container>

              </div>

              <div>

                <md-input-container>
                  <label>Sektionen Qualifikation</label>
                  <input ng-model="vm.event.sectionCountQualification" type="number" required>
                </md-input-container>

                <md-input-container>
                  <md-checkbox ng-model="vm.event.sectionQualificationWithTime">Zusätzlich mit Zeitsektion</md-checkbox>
                  <md-tooltip>Zeitsektion ist Sektion 10</md-tooltip>
                </md-input-container>

                <md-input-container>
                  <label>Anzeige Reihenfolge</label>
                  <md-tooltip>zum Beispiel 1,10,2,3,4,5,6</md-tooltip>
                  <input ng-model="vm.event.qualificationDisplayOrder" type="text" required>
                </md-input-container>


              </div>

              <div>

                <md-input-container>
                  <label>Sektionen Finale</label>
                  <input ng-model="vm.event.sectionCountFinal" type="number">
                </md-input-container>

                <md-input-container>
                  <md-checkbox ng-model="vm.event.sectionFinalWithTime">Zusätzlich mit Zeitsektion</md-checkbox>
                  <md-tooltip>Zeitsektion ist Sektion 10</md-tooltip>
                </md-input-container>

                <md-input-container>
                  <label>Anzeige Reihenfolge</label>
                  <md-tooltip>zum Beispiel 1,10,2,3,4,5,6</md-tooltip>
                  <input ng-model="vm.event.finalDisplayOrder" type="text" required>
                </md-input-container>

              </div>

              <div>

                <md-input-container>
                  <label>Fahreranzahl</label>
                  <input ng-model="vm.event.driverAmount" type="number" required>
                </md-input-container>

                <md-input-container>
                  <label>Automatisch Qualifiziert</label>
                  <md-tooltip>(ersten x Fahrer)</md-tooltip>
                  <input ng-model="vm.event.preQualified" type="number">
                </md-input-container>

                <md-input-container>
                  <label>Im Sudden death</label>
                  <md-tooltip>(x Fahrer)</md-tooltip>
                  <input ng-model="vm.event.suddenDeth" type="number">
                </md-input-container>

              </div>


              <md-card-actions layout="row" layout-align="end center">
                <md-button ng-click="vm.printEvent()" class="md-raised md-primary">Drucken</md-button>
                <md-button ng-click="vm.updateEvent()" class="md-raised md-primary"
                  ng-disabled="eventForm.$invalid || eventForm.$pristine">Speichern
                </md-button>
                <md-button ng-click="vm.resetScore(vm.event)" class="md-raised md-warn">Reset Score</md-button>
              </md-card-actions>

            </form>

          </md-card>

        </div>

        <div>

          <md-card flex class="login-card">

            <md-card-title-text>
              <span class="md-headline">Sektionssponsor</span>
            </md-card-title-text>

            <span style="padding-top: 20px;"></span>

            <form ng-repeat="sponsoring in vm.event.sponsoring" novalidate name="sponsorForm" layout="column"
              layout-gt-sm="row" flex>


              <md-input-container>
                <label>Sektion</label>
                <input ng-model="sponsoring.section" type="number" required>
              </md-input-container>
              <md-input-container>
                <label>Name</label>
                <input ng-model="sponsoring.name" type="text" required>
              </md-input-container>

              <md-input-container>
                <label for="picture">Bild hochladen</label>

                <input id="picture" label="Bild hochladen" name="picture" ngf-select="vm.upload($file, sponsoring)"
                  ng-model="picture" accept="image/*" ngf-max-size="5MB">
                <img hide ngf-thumbnail="picture">

              </md-input-container>
              <img style="height: 50px; width: auto" src="{{  .imageDataURL}}" />

              <md-input-container flex>
                <label>Runde</label>
                <input ng-model="sponsoring.round" type="number" required>
                <md-tooltip>1 = Vorrunde, 2 = Finale, 3 = Beide</md-tooltip>
              </md-input-container>

              <md-button class="md-icon-button md-warn md-raised" ng-click="vm.deleteSponsoring(sponsoring)">
                <md-icon>delete</md-icon>
                <md-tooltip>Lösche Sponsoring</md-tooltip>
              </md-button>
              <md-button class="md-icon-button md-primary md-raised" ng-click="vm.saveSponsoring()">
                <md-icon>save</md-icon>
                <md-tooltip>Speichern</md-tooltip>
              </md-button>
            </form>

            <md-card-actions layout="row" layout-align="end center">
              <md-button ng-click="vm.addSponsoring()" class="md-raised md-primary">Hinzufügen</md-button>
            </md-card-actions>

          </md-card>

        </div>

        <div>

          <md-card flex class="login-card">

            <md-card-title-text>
              <span class="md-headline">Hauptsponsor</span>
            </md-card-title-text>

            <span style="padding-top: 20px;"></span>

            <form novalidate name="mainSponsorForm" layout="column" layout-gt-sm="row" flex>

              <md-input-container>
                <label for="mainPicture">Bild hochladen</label>

                <input id="mainPicture" label="Bild hochladen" name="mainPicture" ngf-select="vm.uploadMain($file)"
                  ng-model="mainPicture" accept="image/*" ngf-max-size="5MB">
                <img hide ngf-thumbnail="mainPicture">

              </md-input-container>

              <img style="height: 50px; width: auto" src="{{vm.event.mainImage}}" />

              <md-button class="md-icon-button md-warn md-raised" ng-click="vm.deleteMainSponsoring(sponsoring)">
                <md-icon>delete</md-icon>
                <md-tooltip>Löschen</md-tooltip>
              </md-button>
              <md-button class="md-icon-button md-primary md-raised" ng-click="vm.saveMainSponsoring()">
                <md-icon>save</md-icon>
                <md-tooltip>Speichern</md-tooltip>
              </md-button>
            </form>
          </md-card>

        </div>

        <div>

          <md-card flex class="login-card">

            <md-card-title-text>
              <span class="md-headline">Nebensponsor</span>
            </md-card-title-text>

            <span style="padding-top: 20px;"></span>

            <form novalidate name="sponsorForm" layout="column" layout-gt-sm="row" flex>

              <md-input-container>
                <label for="secondaryPicture">Bild hochladen</label>

                <input id="secondaryPicture" label="Bild hochladen" name="secondaryPicture"
                  ngf-select="vm.uploadSecondary($file)" ng-model="secondaryPicture" accept="image/*"
                  ngf-max-size="5MB">
                <img hide ngf-thumbnail="secondaryPicture">

              </md-input-container>

              <img style="height: 50px; width: auto" src="{{vm.event.secondaryImage}}" />


              <md-button class="md-icon-button md-warn md-raised" ng-click="vm.deleteSecondarySponsoring()">
                <md-icon>delete</md-icon>
                <md-tooltip>Lösche Sponsoring</md-tooltip>
              </md-button>
              <md-button class="md-icon-button md-primary md-raised" ng-click="vm.saveSecondarySponsoring()">
                <md-icon>save</md-icon>
                <md-tooltip>Speichern</md-tooltip>
              </md-button>
            </form>



          </md-card>

        </div>


      </md-tab-body>


    </md-tab>

    <md-tab>

      <md-tab-label>Starterliste</md-tab-label>

      <md-tab-body>

        <ng-include src="'views/admin/events/drivers.html'"></ng-include>

      </md-tab-body>


    </md-tab>

    <md-tab label="Ergebnisse">


      <md-content layout="column" layout-fill flex>

        <ng-include src="'views/admin/events/scores.html'"></ng-include>

      </md-content>

    </md-tab>

  </md-tabs>


</md-content>
