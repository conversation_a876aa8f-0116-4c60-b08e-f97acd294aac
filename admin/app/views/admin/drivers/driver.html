<md-content layout-fill>

  <md-card>

    <md-card-title-text>
      <span class="md-headline"><PERSON><PERSON><PERSON> hinzufügen</span>
    </md-card-title-text>

    <span style="padding-top: 20px;"></span>

    <form name="driverForm" novalidate layout="column">

      <div layout-gt-xs="row" layout="column">

        <md-input-container flex-gt-xs="50">
          <label>Vorname</label>
          <input ng-model="vm.driver.firstname" type="text" required>
        </md-input-container>

        <md-input-container flex-gt-xs="50">
          <label>Nachname</label>
          <input ng-model="vm.driver.lastname" type="text" required>
        </md-input-container>

      </div>

      <div layout-gt-xs="row" layout="column">

        <md-input-container flex-gt-xs="50">
          <label>Geburtstag</label>
          <input ng-model="vm.driver.dob" type="text" required>
        </md-input-container>

        <md-input-container flex-gt-xs="50">
          <label>Land</label>
          <input ng-model="vm.driver.country" type="text" required>
        </md-input-container>

        <md-input-container flex-gt-xs="33">
          <label>Motorrad</label>
          <input ng-model="vm.driver.bike" type="text" required>
        </md-input-container>

      </div>


      <md-card-actions layout="row" layout-align="end center">
        <md-button ng-click="vm.updateDriver()" class="md-raised md-primary"
                   ng-disabled="driverForm.$invalid || driverForm.$pristine">
          Fahrer speichern
        </md-button>



      </md-card-actions>

    </form>

  </md-card>


</md-content>
