<md-content layout-fill ng-controller="LoginCtrl as vm" layout="row" layout-align="space-around center">

  <md-card class="login-card">

    <md-card-title-text>
      <span class="md-headline">Login zur  Administration</span>
    </md-card-title-text>

    <span style="padding-top: 20px;"></span>

    <form name="loginForm" novalidate layout="column">

      <md-input-container>
        <label>Email</label>
        <input ng-model="vm.user.email" type="email" required>
      </md-input-container>

      <md-input-container>
        <label>Password</label>
        <input ng-model="vm.user.password" type="password" required>
      </md-input-container>

      <md-card-actions layout="row" layout-align="end center">
        <md-button ng-click="vm.login()" class="md-raised md-primary" ng-disabled="loginForm.$invalid || loginForm.$pristine">Anmelden</md-button>
      </md-card-actions>

    </form>

  </md-card>

</md-content>
