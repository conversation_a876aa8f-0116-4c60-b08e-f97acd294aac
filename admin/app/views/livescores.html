<md-content layout-fill ng-controller="LivescoreCtrl as vm" layout="column" ng-cloak>

  <md-toolbar class="md-small live-header">
    <h2 class="md-toolbar-tools live-header">
      <img ng-if="vm.event.mainImage" class="live-logo" src="{{vm.event.mainImage}}">
      <img ng-if="vm.showDefaultMain" class="live-logo" hide-md hide-lg hide-xl src="../images/adac_hallentrial.png">
      <img ng-if="vm.showDefaultMain" class="live-logo live-logo-yellow" hide-xs hide-sm show-md show-lg show-xl src="../images/adac_hallentrail_yellow.png">
      <span class="title-text" flex>{{vm.event.name + ' - ' + vm.roundName}}</span>

      <!--
      <md-checkbox hidden=""  ng-change="vm.roundChanged(1)" ng-model="vm.selectQualification">Q</md-checkbox>
      <md-checkbox hidden=""  ng-change="vm.roundChanged(2)" ng-model="vm.selectFinal">F</md-checkbox>
  -->

      <div layout="row" style="align-items: center">
        <span>Q</span>
        <md-switch ng-model="vm.flip" ng-change="vm.flipped($event)"></md-switch>
        <span>F</span>
      </div>


      <img ng-if="vm.event.secondaryImage" hide-xs hide-sm class="live-logo" src="{{vm.event.secondaryImage}}">
    </h2>
  </md-toolbar>

  <div flex ng-repeat="d in vm.driverScores" layout="row" layout-align="center center"
    ng-class="vm.getClass($index,d.driver)">

    <div class="position">
      {{$index+1}}.
    </div>

    <div ng-if="vm.roundName === 'Qualifikation'" class="icon-div">

      <div ng-if="$index+1 <= vm.event.preQualified">
        <span class="glyphicon glyphicon-ok icon-qualified"></span>
      </div>

      <div
        ng-if="vm.isDriverInSuddenDeath($index) && !d.driver.suddenDeathQualified">
        <span class="glyphicon glyphicon-fire icon-suddendeath"></span>
      </div>

      <div
        ng-if="vm.isDriverInSuddenDeath($index) && d.driver.suddenDeathQualified === true">
        <span class="glyphicon glyphicon-ok icon-suddendeath-qualified"></span>
      </div>


      <div ng-if="$index+1 > (vm.event.preQualified + vm.event.suddenDeth)">
        <span class="glyphicon glyphicon-remove icon-not-qualified"></span>
      </div>

    </div>

    <div class="driver-name-wrapper" layout="row" layout-align="start start">
      <div class="driver-name">
        {{d.driver.driverNumber}}
      </div>
      <div flex>
        {{d.driver.driverRef.firstname}} {{d.driver.driverRef.lastname}}
      </div>

    </div>

    <div ng-if="vm.round === 1" ng-repeat="section in vm.firstRoundDisplayOrder" flex="10" layout="column">
      <div ng-if="section != 10" flex class="score-display">
        {{d.driver.scores[d.round][section]}}
      </div>

      <div class="timeSection" ng-if="section == 10 && d.driver.scores[d.round][section]" layout="column"
        layout-align="center center" ng-cloak="">
        <div>{{d.driver.scores[d.round][section] | number: 2}}</div>
        <img class="sponsoringImage" ng-if="d.sponsoring" ng-src="{{d.sponsoring.imageDataURL}}">
      </div>

      <div ng-if="d.driver.scores[d.round][section] == undefined">
        -
      </div>

    </div>

    <div ng-if="vm.round === 2" ng-repeat="section in vm.secondRoundDisplayOrder" flex="10" layout="column">
      <div ng-if="section != 10" flex class="score-display">
        {{d.driver.scores[d.round][section]}}
      </div>

      <div class="timeSection" ng-if="section == 10 && d.driver.scores[d.round][section]" layout="column"
        layout-align="center center" ng-cloak="">
        <div>{{d.driver.scores[d.round][section]}}</div>
        <img class="sponsoringImage" ng-if="d.sponsoring" ng-src="{{d.sponsoring.imageDataURL}}">
      </div>

      <div ng-if="d.driver.scores[d.round][section] == undefined">
        -
      </div>

    </div>

    <div flex="10" style="font-weight: bold">
      {{d.pointCount}}
    </div>

  </div>


</md-content>
