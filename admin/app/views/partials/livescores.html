<md-content layout-fill layout="column">

  <div flex ng-repeat="d in vm.driverScores" layout="row" layout-align="center center"
       ng-class="vm.getClass($index,d.driver)">

    <div class="position">
      {{$index+1}}.
    </div>

    <div ng-if="vm.roundName === 'Qualifikation'" class="icon-div">

      <div ng-if="$index+1 <= vm.event.preQualified">
        <span class="glyphicon glyphicon-ok icon-qualified"></span>
      </div>

      <div
        ng-if="($index+1 == vm.event.preQualified+1 || $index+1 == vm.event.preQualified+2) && !d.driver.suddenDeathQualified">
        <span class="glyphicon glyphicon-fire icon-suddendeath"></span>
      </div>

      <div
        ng-if="($index+1 == vm.event.preQualified+1 || $index+1 == vm.event.preQualified+2) && d.driver.suddenDeathQualified === true">
        <span class="glyphicon glyphicon-ok icon-suddendeath-qualified"></span>
      </div>


      <div ng-if="$index+1 > vm.event.preQualified+2">
        <span class="glyphicon glyphicon-remove icon-not-qualified"></span>
      </div>

    </div>


    <div flex="55" style="text-align: left;">
      {{d.driver.driverNumber}} - {{d.driver.driverRef.firstname}} {{d.driver.driverRef.lastname}}
    </div>

    <div ng-if="vm.round === 1" ng-repeat="section in vm.firstRoundDisplayOrder" flex="10" layout="column">
      <div ng-if="section != 10" flex class="score-display">
        {{d.driver.scores[d.round][section]}}
      </div>

      <div class="timeSection" ng-if="section == 10 && d.driver.scores[d.round][section]" layout="column"
           layout-align="center center" ng-cloak="">
        <div>{{d.driver.scores[d.round][section]}}</div>
        <img class="sponsoringImage" ng-if="d.sponsoring" ng-src="{{d.sponsoring.imageDataURL}}">
      </div>

      <div ng-if="d.driver.scores[d.round][section] == undefined">
        -
      </div>

    </div>

    <div ng-if="vm.round === 2" ng-repeat="section in vm.secondRoundDisplayOrder" flex="10" layout="column">
      <div ng-if="section != 10" flex class="score-display">
        {{d.driver.scores[d.round][section]}}
      </div>

      <div class="timeSection" ng-if="section == 10 && d.driver.scores[d.round][section]" layout="column"
           layout-align="center center" ng-cloak="">
        <div>{{d.driver.scores[d.round][section]}}</div>
        <img class="sponsoringImage" ng-if="d.sponsoring" ng-src="{{d.sponsoring.imageDataURL}}">
      </div>

      <div ng-if="d.driver.scores[d.round][section] == undefined">
        -
      </div>

    </div>

    <div flex="10" style="font-weight: bold">
      {{d.pointCount}}
    </div>

  </div>

</md-content>
